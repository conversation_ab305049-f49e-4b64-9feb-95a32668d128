apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-exporter
  namespace: redis
  labels:
    app: redis-exporter
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis-exporter
  template:
    metadata:
      labels:
        app: redis-exporter
    spec:
      containers:
      - name: redis-exporter
        image: oliver006/redis_exporter:latest
        ports:
        - containerPort: 9121
          name: metrics
        env:
        - name: REDIS_ADDR
          value: "redis://redis-service.redis.svc.cluster.local:6379"
        - name: REDIS_EXPORTER_LOG_FORMAT
          value: "txt"
        - name: REDIS_EXPORTER_DEBUG
          value: "false"
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
        livenessProbe:
          httpGet:
            path: /metrics
            port: 9121
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /metrics
            port: 9121
          initialDelaySeconds: 5
          periodSeconds: 5
