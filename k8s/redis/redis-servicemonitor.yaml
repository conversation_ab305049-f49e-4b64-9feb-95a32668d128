# redis-servicemonitor.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: redis-exporter-servicemonitor
  namespace: redis  # ServiceMonitor no mesmo namespace da aplicação
  labels:
    app: redis-exporter
    release: kube-prometheus-stack  # Label importante para descoberta
spec:
  selector:
    matchLabels:
      app: redis-exporter
  endpoints:
  - port: metrics
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s