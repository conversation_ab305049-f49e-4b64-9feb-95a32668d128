apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: conversas-ai-worker
  namespace: conversas-ai
  labels:
    app: conversas-ai
    component: worker
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: conversas-ai-worker
  pollingInterval: 5                 
  cooldownPeriod: 180             
  minReplicaCount: 1                 
  maxReplicaCount: 40                
  advanced:
    horizontalPodAutoscalerConfig:
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 300 
          policies:
          - type: Percent
            value: 20  
            periodSeconds: 180  
          - type: Pods
            value: 2   
            periodSeconds: 180
          selectPolicy: Min 
        scaleUp:
          stabilizationWindowSeconds: 30  
          policies:
          - type: Percent
            value: 100 
            periodSeconds: 30 
          - type: Pods
            value: 4   
            periodSeconds: 30
          selectPolicy: Max  
  triggers:
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:80
      # 6 é o número de treads por pod configurado em DELAYED_QUEUE_MAX_WORKERS no configmap k8s/conversas-ai/worker/configmap.yaml
      query: clamp_min(max(query{query="components.DelayedQueueWorker.common.mensagens_a_processar_agora"}) - (count(query{query="components.DelayedQueueWorker.common.mensagens_a_processar_agora"}) * 6), 0)
      threshold: '5'           # Escala quando sobrecarga > 5 mensagens
      activationThreshold: '1' # Ativa quando sobrecarga > 1 mensagem
  - type: cpu
    metadata:
      type: Utilization
      value: "75"                   
  - type: memory
    metadata:
      type: Utilization
      value: "90"  
