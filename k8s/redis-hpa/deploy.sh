#!/bin/bash

# Redis HPA Deployment Script
# This script deploys Redis with HPA to Kubernetes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="redis-hpa"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check metrics server
    if ! kubectl get deployment metrics-server -n kube-system &> /dev/null; then
        log_warning "Metrics Server not found. HPA may not work properly."
        log_info "To install Metrics Server, run:"
        log_info "kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml"
    fi
    
    log_success "Prerequisites check completed"
}

deploy_resources() {
    log_info "Deploying Redis with HPA..."
    
    # Apply resources in order
    local resources=(
        "namespace.yaml"
        "configmap.yaml"
        "pvc.yaml"
        "deployment.yaml"
        "service.yaml"
        "hpa.yaml"
    )
    
    for resource in "${resources[@]}"; do
        if [[ -f "$SCRIPT_DIR/$resource" ]]; then
            log_info "Applying $resource..."
            kubectl apply -f "$SCRIPT_DIR/$resource"
        else
            log_warning "File $resource not found, skipping..."
        fi
    done
    
    # Ask about ingress
    read -p "Do you want to deploy the ingress? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [[ -f "$SCRIPT_DIR/ingress.yaml" ]]; then
            log_info "Applying ingress.yaml..."
            kubectl apply -f "$SCRIPT_DIR/ingress.yaml"
        else
            log_warning "ingress.yaml not found"
        fi
    fi
    
    log_success "Resources deployed successfully"
}

wait_for_deployment() {
    log_info "Waiting for deployment to be ready..."
    
    kubectl wait --for=condition=available --timeout=300s deployment/redis -n $NAMESPACE
    
    log_success "Deployment is ready"
}

show_status() {
    log_info "Checking deployment status..."
    
    echo
    echo "=== Namespace ==="
    kubectl get namespace $NAMESPACE
    
    echo
    echo "=== Pods ==="
    kubectl get pods -n $NAMESPACE -o wide
    
    echo
    echo "=== Services ==="
    kubectl get services -n $NAMESPACE
    
    echo
    echo "=== HPA ==="
    kubectl get hpa -n $NAMESPACE
    
    echo
    echo "=== PVC ==="
    kubectl get pvc -n $NAMESPACE
    
    if kubectl get ingress -n $NAMESPACE &> /dev/null; then
        echo
        echo "=== Ingress ==="
        kubectl get ingress -n $NAMESPACE
    fi
}

show_connection_info() {
    log_info "Connection information:"
    
    echo
    echo "=== Internal Access (within cluster) ==="
    echo "Host: redis-service.$NAMESPACE.svc.cluster.local"
    echo "Port: 6379"
    
    echo
    echo "=== Port Forward (for testing) ==="
    echo "kubectl port-forward service/redis-service 6379:6379 -n $NAMESPACE"
    
    echo
    echo "=== Test Connection ==="
    echo "kubectl run redis-test --image=redis:alpine --rm -it --restart=Never -- redis-cli -h redis-service.$NAMESPACE.svc.cluster.local ping"
    
    if kubectl get ingress -n $NAMESPACE &> /dev/null; then
        echo
        echo "=== External Access (via Ingress) ==="
        kubectl get ingress -n $NAMESPACE -o jsonpath='{.items[0].spec.rules[0].host}'
        echo
    fi
}

show_monitoring_commands() {
    log_info "Monitoring commands:"
    
    echo
    echo "=== Watch HPA ==="
    echo "watch kubectl get hpa -n $NAMESPACE"
    
    echo
    echo "=== HPA Details ==="
    echo "kubectl describe hpa redis-hpa -n $NAMESPACE"
    
    echo
    echo "=== Pod Metrics ==="
    echo "kubectl top pods -n $NAMESPACE"
    
    echo
    echo "=== Logs ==="
    echo "kubectl logs -f deployment/redis -n $NAMESPACE"
    
    echo
    echo "=== Events ==="
    echo "kubectl get events -n $NAMESPACE --sort-by='.lastTimestamp'"
}

cleanup() {
    log_warning "This will delete all Redis HPA resources!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "Deleting namespace $NAMESPACE..."
        kubectl delete namespace $NAMESPACE
        log_success "Cleanup completed"
    else
        log_info "Cleanup cancelled"
    fi
}

show_help() {
    echo "Redis HPA Deployment Script"
    echo
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  deploy     Deploy Redis with HPA (default)"
    echo "  status     Show deployment status"
    echo "  cleanup    Delete all resources"
    echo "  help       Show this help message"
    echo
    echo "Examples:"
    echo "  $0                 # Deploy Redis with HPA"
    echo "  $0 deploy          # Deploy Redis with HPA"
    echo "  $0 status          # Show status"
    echo "  $0 cleanup         # Delete all resources"
}

# Main script
main() {
    local command="${1:-deploy}"
    
    case $command in
        deploy)
            check_prerequisites
            deploy_resources
            wait_for_deployment
            show_status
            show_connection_info
            show_monitoring_commands
            ;;
        status)
            show_status
            show_connection_info
            show_monitoring_commands
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
