apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: redis-ingress
  namespace: redis-hpa
  labels:
    app: redis
  annotations:
    # Nginx ingress controller annotations
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    # TCP stream for Redis protocol
    nginx.ingress.kubernetes.io/tcp-services-configmap: redis-hpa/redis-tcp
    # Rate limiting (optional)
    nginx.ingress.kubernetes.io/rate-limit-connections: "10"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    # Custom timeout for Redis connections
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
spec:
  ingressClassName: nginx
  rules:
  - host: redis-hpa.local  # Change to your domain
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: redis-service
            port:
              number: 6379
  # Uncomment for TLS
  # tls:
  # - hosts:
  #   - redis-hpa.local
  #   secretName: redis-tls-secret

---
# ConfigMap for TCP services (required for Redis protocol over Ingress)
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-tcp
  namespace: redis-hpa
  labels:
    app: redis
data:
  6379: "redis-hpa/redis-service:6379"
