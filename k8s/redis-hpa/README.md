# Redis com HPA (Horizontal Pod Autoscaler)

Este diretório contém a configuração para deploy do Redis Open Source com Horizontal Pod Autoscaler (HPA) no Kubernetes.

## Visão Geral

Esta implementação utiliza Redis Open Source com HPA para escalonamento automático baseado em métricas de CPU e memória. É uma solução mais simples e adequada para a maioria dos casos de uso, comparada ao Redis Enterprise.

## Arquitetura

- **Redis Open Source**: Versão community do Redis
- **HPA**: Escalonamento automático baseado em CPU/memória
- **PVC**: Armazenamento persistente para dados
- **ConfigMap**: Configurações customizadas do Redis
- **Service**: Exposição interna do serviço
- **Ingress**: Exposição externa (opcional)

## Pré-requisitos

- Cluster Kubernetes funcional
- Metrics Server instalado no cluster
- kubectl configurado
- Namespace `redis-hpa` (será criado automaticamente)

## Verificar Metrics Server

Antes de aplicar o HPA, verifique se o Metrics Server está funcionando:

```bash
kubectl get deployment metrics-server -n kube-system
kubectl top nodes
kubectl top pods -n kube-system
```

Se o Metrics Server não estiver instalado, instale com:

```bash
kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
```

## Estrutura dos Arquivos

- `namespace.yaml` - Namespace dedicado
- `configmap.yaml` - Configurações do Redis
- `pvc.yaml` - Persistent Volume Claim
- `deployment.yaml` - Deployment do Redis
- `service.yaml` - Service para exposição interna
- `hpa.yaml` - Horizontal Pod Autoscaler
- `ingress.yaml` - Ingress para exposição externa (opcional)

## Deploy

### 1. Aplicar todos os recursos

```bash
# Aplicar todos os arquivos de uma vez
kubectl apply -f k8s/redis-hpa/

# Ou aplicar individualmente na ordem
kubectl apply -f k8s/redis-hpa/namespace.yaml
kubectl apply -f k8s/redis-hpa/configmap.yaml
kubectl apply -f k8s/redis-hpa/pvc.yaml
kubectl apply -f k8s/redis-hpa/deployment.yaml
kubectl apply -f k8s/redis-hpa/service.yaml
kubectl apply -f k8s/redis-hpa/hpa.yaml
kubectl apply -f k8s/redis-hpa/ingress.yaml  # opcional
```

### 2. Verificar o deploy

```bash
# Verificar pods
kubectl get pods -n redis-hpa

# Verificar HPA
kubectl get hpa -n redis-hpa

# Verificar métricas do HPA
kubectl describe hpa redis-hpa -n redis-hpa

# Verificar logs
kubectl logs -f deployment/redis -n redis-hpa
```

## Configuração do HPA

O HPA está configurado com:

- **Min replicas**: 1
- **Max replicas**: 10
- **Target CPU**: 70%
- **Target Memory**: 80%
- **Scale down**: Estabilização de 300s
- **Scale up**: Estabilização de 60s

### Personalizar HPA

Para ajustar os limites, edite o arquivo `hpa.yaml`:

```yaml
spec:
  minReplicas: 2      # Mínimo de pods
  maxReplicas: 20     # Máximo de pods
  targetCPUUtilizationPercentage: 60    # CPU alvo
  targetMemoryUtilizationPercentage: 70 # Memória alvo
```

## Monitoramento

### Verificar status do HPA

```bash
# Status atual
kubectl get hpa redis-hpa -n redis-hpa

# Detalhes e eventos
kubectl describe hpa redis-hpa -n redis-hpa

# Métricas em tempo real
watch kubectl get hpa redis-hpa -n redis-hpa
```

### Testar escalonamento

```bash
# Gerar carga para testar o HPA
kubectl run -i --tty load-generator --rm --image=busybox --restart=Never -- /bin/sh

# Dentro do pod, execute:
while true; do wget -q -O- http://redis-service.redis-hpa.svc.cluster.local:6379; done
```

## Acesso ao Redis

### Interno (dentro do cluster)

```bash
# Host: redis-service.redis-hpa.svc.cluster.local
# Porta: 6379
```

### Externo (via Ingress)

Se o ingress estiver configurado:

```bash
# Verificar ingress
kubectl get ingress -n redis-hpa

# Acessar via URL configurada no ingress
```

### Port-forward para teste

```bash
kubectl port-forward service/redis-service 6379:6379 -n redis-hpa
```

## Configurações Avançadas

### Recursos do Pod

Os recursos estão configurados em `deployment.yaml`:

```yaml
resources:
  requests:
    cpu: "100m"
    memory: "128Mi"
  limits:
    cpu: "500m"
    memory: "512Mi"
```

### Configuração do Redis

Personalize as configurações no `configmap.yaml`:

```yaml
data:
  redis.conf: |
    maxmemory 256mb
    maxmemory-policy allkeys-lru
    save 900 1
    save 300 10
    save 60 10000
```

## Troubleshooting

### HPA não está funcionando

```bash
# Verificar se o Metrics Server está funcionando
kubectl get apiservice v1beta1.metrics.k8s.io -o yaml

# Verificar métricas dos pods
kubectl top pods -n redis-hpa

# Verificar eventos do HPA
kubectl get events -n redis-hpa --sort-by='.lastTimestamp'
```

### Pod não está iniciando

```bash
# Verificar logs
kubectl logs -f deployment/redis -n redis-hpa

# Verificar eventos do pod
kubectl describe pod -l app=redis -n redis-hpa

# Verificar PVC
kubectl get pvc -n redis-hpa
```

### Problemas de conectividade

```bash
# Testar conectividade interna
kubectl run redis-test --image=redis:alpine --rm -it --restart=Never -- redis-cli -h redis-service.redis-hpa.svc.cluster.local ping

# Verificar service
kubectl get svc -n redis-hpa
kubectl describe svc redis-service -n redis-hpa
```

## Limpeza

Para remover todos os recursos:

```bash
kubectl delete namespace redis-hpa
```

## Comparação: Redis Open Source vs Redis Enterprise

### Redis Open Source (Esta implementação)
- ✅ Simples de configurar e manter
- ✅ Menor consumo de recursos
- ✅ Adequado para a maioria dos casos
- ✅ HPA nativo do Kubernetes
- ❌ Sem recursos empresariais avançados
- ❌ Sem clustering automático

### Redis Enterprise
- ✅ Recursos empresariais (clustering, backup, etc.)
- ✅ Alta disponibilidade automática
- ✅ Monitoramento avançado
- ❌ Mais complexo de configurar
- ❌ Maior consumo de recursos
- ❌ Requer licença para recursos completos

## Próximos Passos

1. **Monitoramento**: Integrar com Prometheus/Grafana
2. **Backup**: Configurar backup automático dos dados
3. **Segurança**: Adicionar autenticação e TLS
4. **Alta Disponibilidade**: Considerar Redis Sentinel ou Cluster
